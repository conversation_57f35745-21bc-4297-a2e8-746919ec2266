#!/usr/bin/env python3
"""
Test Debug Logging Functionality
===============================

This script tests the debug logging functionality without running the full
Magnetic-One system. It's useful for verifying that the debug logging
components work correctly.

Usage:
    python test_debug_logging.py
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timezone

# Add the scripts directory to the path so we can import utils
sys.path.insert(0, str(Path(__file__).parent))

from utils import setup_debug_logging

def test_debug_logging():
    """Test the debug logging functionality."""
    print("Testing Debug Logging Functionality")
    print("=" * 50)
    
    # Setup test parameters
    scenario_id = "test"
    endpoint_type = "cloudgpt"
    model = "gpt-4o-mini-20240718"
    output_dir = Path(__file__).parent.parent / "logs/generated"
    
    # Create debug logger
    debug_logger = setup_debug_logging(scenario_id, endpoint_type, model, output_dir)
    
    print(f"✓ Debug logger created")
    print(f"✓ Debug log path: {debug_logger.debug_log_path}")
    
    # Test basic logging
    debug_logger.log_debug("Starting test execution")
    debug_logger.log_debug("This is a test debug message", "INFO")
    
    # Test agent interaction logging
    debug_logger.log_agent_interaction("TestAgent", "initialize", "Agent initialized successfully", 1)
    debug_logger.log_agent_interaction("TestAgent", "process", "Processing test query: What is 2+2?", 2)
    
    # Test LLM call logging
    test_messages = [
        {"role": "user", "content": "What is 2+2?"},
        {"role": "system", "content": "You are a helpful assistant."}
    ]
    test_response = {
        "content": "2+2 equals 4",
        "usage": {"prompt_tokens": 15, "completion_tokens": 8}
    }
    debug_logger.log_llm_call(test_messages, test_response, "TestAgent")
    
    # Test execution step logging
    debug_logger.log_execution_step(1, "user", "What is 2+2?")
    debug_logger.log_execution_step(2, "assistant", "2+2 equals 4")
    
    # Test error logging
    try:
        raise ValueError("This is a test error")
    except Exception as e:
        debug_logger.log_error(e, "test_function")
    
    # Test function tracing (briefly)
    print("✓ Testing function tracing...")
    debug_logger.start_tracing()
    
    # Call some functions to generate trace data
    def test_function_a():
        time.sleep(0.1)
        test_function_b()
    
    def test_function_b():
        time.sleep(0.1)
        return "test result"
    
    test_function_a()
    debug_logger.stop_tracing()
    
    # Finalize debug logging
    debug_logger.finalize(2, "00:00:05")
    
    print("\n" + "=" * 50)
    print("Debug Logging Test Completed Successfully!")
    print(f"✓ Debug log file: {debug_logger.debug_log_path}")
    print(f"✓ Debug JSON file: {debug_logger.debug_log_path.with_suffix('.json')}")
    
    # Show some statistics
    print(f"✓ Function calls traced: {len(debug_logger.debug_data['function_calls'])}")
    print(f"✓ Agent interactions: {len(debug_logger.debug_data['agent_interactions'])}")
    print(f"✓ LLM calls: {len(debug_logger.debug_data['llm_calls'])}")
    print(f"✓ Errors logged: {len(debug_logger.debug_data['errors'])}")
    print(f"✓ Execution steps: {len(debug_logger.debug_data['execution_trace'])}")
    
    # Show file contents preview
    print("\nDebug Log Preview (first 10 lines):")
    print("-" * 30)
    with debug_logger.debug_log_path.open("r", encoding="utf-8") as f:
        for i, line in enumerate(f):
            if i >= 10:
                break
            print(f"{i+1:2d}: {line.rstrip()}")
    
    return debug_logger.debug_log_path

if __name__ == "__main__":
    test_debug_logging()

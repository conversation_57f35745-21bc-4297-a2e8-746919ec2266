#!/usr/bin/env python3
"""
Debug Level Logging for Magnetic-One Test Script
===============================================

This module provides debug-level logging functionality that saves detailed
execution traces to log/generated/debug/ directory with minimal code modification.
"""

import os
import json
import logging
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional


class DebugLogger:
    """Debug logger that captures detailed execution information."""
    
    def __init__(self, scenario_id: str, endpoint_type: str, model: str, 
                 output_dir: Path, run_timestamp: str = None):
        """
        Initialize debug logger.
        
        Args:
            scenario_id: Scenario ID (e.g. "1")
            endpoint_type: Endpoint type (e.g. "ollama", "openai", "cloudgpt")
            model: Model name (e.g. "llama3.1", "gpt-4o-mini-20240718")
            output_dir: Base output directory
            run_timestamp: Run timestamp for file naming
        """
        self.scenario_id = scenario_id
        self.endpoint_type = endpoint_type
        self.model = model
        self.run_timestamp = run_timestamp or self._generate_timestamp()
        
        # Create debug directory
        self.debug_dir = output_dir / "debug"
        self.debug_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup debug log file
        self.debug_log_path = self.debug_dir / f"scenario_{scenario_id}_{endpoint_type}_{model}_{self.run_timestamp}_debug.log"
        
        # Initialize debug log
        self._init_debug_log()
        
        # Setup debug data collection
        self.debug_data = {
            "metadata": {
                "scenario_id": scenario_id,
                "endpoint_type": endpoint_type,
                "model": model,
                "run_timestamp": self.run_timestamp,
                "start_time": datetime.now(timezone.utc).isoformat(),
                "debug_level": "DETAILED"
            },
            "execution_trace": [],
            "function_calls": [],
            "agent_interactions": [],
            "llm_calls": [],
            "errors": [],
            "performance_metrics": {}
        }
        
        # Setup function call tracing
        self._setup_function_tracing()
        
    def _generate_timestamp(self) -> str:
        """Generate UTC timestamp for file naming."""
        utc_time = datetime.now(timezone.utc)
        return f"{utc_time.year:04d}{utc_time.month:02d}{utc_time.day:02d}_{utc_time.hour:02d}{utc_time.minute:02d}{utc_time.second:02d}UTC"
    
    def _init_debug_log(self):
        """Initialize debug log file with header."""
        start_time = datetime.now(timezone.utc)
        header = f"""=== DEBUG LOG STARTED ===
Scenario: {self.scenario_id}
Endpoint: {self.endpoint_type}
Model: {self.model}
Timestamp: {self.run_timestamp}
Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')} UTC
Debug Level: DETAILED
=== EXECUTION TRACE ===

"""
        self.debug_log_path.write_text(header, encoding="utf-8")
        
    def _setup_function_tracing(self):
        """Setup function call tracing using sys.settrace."""
        self.trace_depth = 0
        self.traced_modules = {'autogen', 'magentic_one', '__main__'}
        
    def trace_function_calls(self, frame, event, arg):
        """Trace function calls for debugging."""
        if event == 'call':
            filename = frame.f_code.co_filename
            function_name = frame.f_code.co_name
            line_number = frame.f_lineno
            
            # Only trace relevant modules
            if any(module in filename for module in self.traced_modules):
                self.trace_depth += 1
                indent = "  " * min(self.trace_depth, 10)
                
                trace_info = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "event": "function_call",
                    "function": function_name,
                    "file": Path(filename).name,
                    "line": line_number,
                    "depth": self.trace_depth
                }
                
                self.debug_data["function_calls"].append(trace_info)
                self.log_debug(f"{indent}→ {function_name}() [{Path(filename).name}:{line_number}]")
                
        elif event == 'return':
            if any(module in frame.f_code.co_filename for module in self.traced_modules):
                self.trace_depth = max(0, self.trace_depth - 1)
                
        return self.trace_function_calls
    
    def log_debug(self, message: str, level: str = "DEBUG"):
        """Log debug message to file."""
        timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        with self.debug_log_path.open("a", encoding="utf-8") as f:
            f.write(log_entry)
    
    def log_agent_interaction(self, agent_name: str, action: str, content: str, step: int = None):
        """Log agent interaction."""
        interaction = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "step": step,
            "agent": agent_name,
            "action": action,
            "content": content[:500] + "..." if len(content) > 500 else content
        }
        
        self.debug_data["agent_interactions"].append(interaction)
        self.log_debug(f"Agent[{agent_name}] {action}: {content[:100]}...")
    
    def log_llm_call(self, messages: List[Dict], response: Dict, agent_id: str = None):
        """Log LLM call details."""
        llm_call = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent_id": agent_id,
            "input_messages_count": len(messages),
            "input_total_chars": sum(len(str(msg.get('content', ''))) for msg in messages),
            "response_chars": len(str(response.get('content', ''))),
            "prompt_tokens": response.get('usage', {}).get('prompt_tokens', 0),
            "completion_tokens": response.get('usage', {}).get('completion_tokens', 0)
        }
        
        self.debug_data["llm_calls"].append(llm_call)
        self.log_debug(f"LLM Call[{agent_id}]: {len(messages)} msgs → {llm_call['response_chars']} chars")
    
    def log_error(self, error: Exception, context: str = ""):
        """Log error with context."""
        error_info = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context
        }
        
        self.debug_data["errors"].append(error_info)
        self.log_debug(f"ERROR[{context}]: {type(error).__name__}: {str(error)}", "ERROR")
    
    def log_execution_step(self, step: int, role: str, content: str, timing_info: Dict = None):
        """Log execution step with detailed information."""
        step_info = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "step": step,
            "role": role,
            "content_length": len(content),
            "content_preview": content[:200] + "..." if len(content) > 200 else content,
            "timing": timing_info
        }
        
        self.debug_data["execution_trace"].append(step_info)
        self.log_debug(f"Step {step} [{role}]: {len(content)} chars - {content[:50]}...")
    
    def start_tracing(self):
        """Start function call tracing."""
        sys.settrace(self.trace_function_calls)
        self.log_debug("Function call tracing started")
    
    def stop_tracing(self):
        """Stop function call tracing."""
        sys.settrace(None)
        self.log_debug("Function call tracing stopped")
    
    def finalize(self, total_steps: int, duration: str):
        """Finalize debug logging and save JSON summary."""
        end_time = datetime.now(timezone.utc)
        
        # Update metadata
        self.debug_data["metadata"]["end_time"] = end_time.isoformat()
        self.debug_data["metadata"]["total_steps"] = total_steps
        self.debug_data["metadata"]["duration"] = duration
        
        # Calculate performance metrics
        self.debug_data["performance_metrics"] = {
            "total_function_calls": len(self.debug_data["function_calls"]),
            "total_agent_interactions": len(self.debug_data["agent_interactions"]),
            "total_llm_calls": len(self.debug_data["llm_calls"]),
            "total_errors": len(self.debug_data["errors"]),
            "total_execution_steps": len(self.debug_data["execution_trace"])
        }
        
        # Save JSON summary
        json_path = self.debug_log_path.with_suffix('.json')
        with json_path.open("w", encoding="utf-8") as f:
            json.dump(self.debug_data, f, indent=2, ensure_ascii=False)
        
        # Add footer to log file
        footer = f"""
=== DEBUG LOG COMPLETED ===
End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')} UTC
Total Steps: {total_steps}
Duration: {duration}
Function Calls: {self.debug_data["performance_metrics"]["total_function_calls"]}
Agent Interactions: {self.debug_data["performance_metrics"]["total_agent_interactions"]}
LLM Calls: {self.debug_data["performance_metrics"]["total_llm_calls"]}
Errors: {self.debug_data["performance_metrics"]["total_errors"]}
JSON Summary: {json_path.name}
=== END DEBUG LOG ===
"""
        
        with self.debug_log_path.open("a", encoding="utf-8") as f:
            f.write(footer)
        
        print(f"✓ Debug logging completed")
        print(f"✓ Debug log: {self.debug_log_path}")
        print(f"✓ Debug JSON: {json_path}")
        print(f"✓ Function calls traced: {self.debug_data['performance_metrics']['total_function_calls']}")


def setup_debug_logging(scenario_id: str, endpoint_type: str, model: str, 
                       output_dir: Path, run_timestamp: str = None) -> DebugLogger:
    """
    Setup debug logging with minimal code modification.
    
    Args:
        scenario_id: Scenario ID
        endpoint_type: Endpoint type
        model: Model name
        output_dir: Output directory
        run_timestamp: Run timestamp
        
    Returns:
        DebugLogger: Configured debug logger instance
    """
    debug_logger = DebugLogger(scenario_id, endpoint_type, model, output_dir, run_timestamp)
    print(f"✓ Debug logging initialized: {debug_logger.debug_log_path}")
    return debug_logger

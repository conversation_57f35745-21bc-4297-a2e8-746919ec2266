#!/usr/bin/env python3
"""
Simple Debug Logging for Magentic-One
====================================

This module provides simple debug-level logging that follows the same pattern
as the existing LLM logging in run_m1_test.py.

Creates debug log files alongside existing logs:
- scenario_{id}_{endpoint}_{model}_{timestamp}.log (existing)
- scenario_{id}_{endpoint}_{model}_{timestamp}_llm_calls.log (existing)
- scenario_{id}_{endpoint}_{model}_{timestamp}_debug.log (new)
"""

from datetime import datetime, timezone
from pathlib import Path


def setup_debug_logging(scenario_id: str, endpoint_type: str, model: str,
                       output_dir: Path, run_timestamp: str) -> Path:
    """
    Setup debug logging following the same pattern as existing logs.

    Args:
        scenario_id: Scenario ID
        endpoint_type: Endpoint type
        model: Model name
        output_dir: Output directory
        run_timestamp: Run timestamp

    Returns:
        Path: Path to the debug log file
    """
    # Create debug log path following existing pattern
    debug_filename = f"scenario_{scenario_id}_{endpoint_type}_{model}_{run_timestamp}_debug.log"
    debug_log_path = output_dir / debug_filename

    # Initialize debug log file
    start_time = datetime.now(timezone.utc)
    with debug_log_path.open("w", encoding="utf-8") as f:
        f.write(f"=== DEBUG LOG STARTED ===\n")
        f.write(f"Scenario: {scenario_id}\n")
        f.write(f"Endpoint: {endpoint_type}\n")
        f.write(f"Model: {model}\n")
        f.write(f"Timestamp: {run_timestamp}\n")
        f.write(f"Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')} UTC\n")
        f.write(f"=== EXECUTION TRACE ===\n\n")

    print(f"✓ Debug logging initialized: {debug_log_path}")
    return debug_log_path


def finalize_debug_logging(debug_log_path: Path, total_steps: int, duration: str):
    """
    Finalize debug logging with summary information.

    Args:
        debug_log_path: Path to the debug log file
        total_steps: Total number of execution steps
        duration: Duration of execution as formatted string
    """
    # Write final summary
    end_time = datetime.now(timezone.utc)
    with debug_log_path.open("a", encoding="utf-8") as f:
        f.write(f"\n=== DEBUG LOG COMPLETED ===\n")
        f.write(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')} UTC\n")
        f.write(f"Total Steps: {total_steps}\n")
        f.write(f"Duration: {duration}\n")
        f.write(f"=== END DEBUG LOG ===\n")

    print(f"✓ Debug logging completed: {debug_log_path}")


def log_debug_message(debug_log_path: Path, message: str):
    """
    Log a debug message to the debug log file.

    Args:
        debug_log_path: Path to the debug log file
        message: Message to log
    """
    timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S.%f")[:-3]
    with debug_log_path.open("a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] DEBUG: {message}\n")


# Backward compatibility - provide a simple DebugLogger class
class DebugLogger:
    """Simple debug logger for backward compatibility."""

    def __init__(self, scenario_id: str, endpoint_type: str, model: str, output_dir: Path, run_timestamp: str = None):
        self.debug_log_path = setup_debug_logging(scenario_id, endpoint_type, model, output_dir, run_timestamp)

    def start_tracing(self):
        log_debug_message(self.debug_log_path, "Function call tracing started")

    def stop_tracing(self):
        log_debug_message(self.debug_log_path, "Function call tracing stopped")

    def log_debug(self, message: str):
        log_debug_message(self.debug_log_path, message)

    def log_execution_step(self, step_number: int, role: str, content: str):
        content_preview = content[:100] + "..." if len(content) > 100 else content
        log_debug_message(self.debug_log_path, f"Step {step_number} [{role}]: {content_preview}")

    def log_agent_interaction(self, agent_name: str, action: str, content: str, step_number: int = None):
        content_preview = content[:100] + "..." if len(content) > 100 else content
        log_debug_message(self.debug_log_path, f"Agent[{agent_name}] {action}: {content_preview}")

    def log_llm_call(self, messages, response, agent_name: str = None):
        response_content = response.get("content", "")
        if isinstance(response_content, dict):
            response_content = str(response_content)
        agent_info = f"[{agent_name}]" if agent_name else ""
        log_debug_message(self.debug_log_path, f"LLM Call{agent_info}: {len(messages)} msgs → {len(response_content)} chars")

    def log_error(self, error: Exception, context: str = None):
        error_msg = f"ERROR[{context}]: {type(error).__name__}: {str(error)}" if context else f"ERROR: {type(error).__name__}: {str(error)}"
        log_debug_message(self.debug_log_path, error_msg)

    def finalize(self, total_steps: int, duration: str):
        finalize_debug_logging(self.debug_log_path, total_steps, duration)

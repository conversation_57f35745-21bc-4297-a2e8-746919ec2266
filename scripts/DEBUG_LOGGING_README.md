# 简单Debug日志记录功能

## 概述

本功能为Magentic-One测试脚本添加了简单的debug级别日志记录，按照run_m1_test.py中原有的日志保存路径，在`logs/generated/`目录下生成debug日志文件。

## 特性

- **最小代码修改**：通过新增`debug_log.py`模块和新的运行脚本实现，不修改原有代码
- **简单debug消息**：记录带时间戳的debug信息
- **代理交互记录**：记录代理通信和响应
- **执行步骤跟踪**：跟踪执行步骤和内容预览
- **错误记录**：捕获异常和上下文信息
- **相同目录结构**：遵循现有日志文件模式
- **实时记录**：在执行过程中增量写入debug信息

## 文件结构

```
scripts/
├── run_m1_debug.py          # 新的运行脚本（包含debug功能）
└── utils/
    ├── debug_log.py         # 简单debug日志实现
    ├── log_format.py        # 原有LLM日志工具
    ├── internal_helpers.py  # 内部辅助函数
    └── __init__.py          # 包导出配置

logs/generated/              # 日志输出目录（与原有相同）
├── scenario_*_debug.log     # 新的debug日志文件
├── scenario_*.log           # 原有执行日志
├── scenario_*.json          # 原有JSON跟踪
└── scenario_*_llm_calls.log # 原有LLM调用日志
```

## 使用方法

### 1. 运行带debug日志的测试

```bash
# 激活conda环境
conda activate m1

# 运行带debug日志的测试
cd /mnt/v-mingm/AutoDebugging/scripts
python run_m1_debug.py --scenario 1 --endpoint_type cloudgpt --model gpt-4o-mini-20240718
```

运行后会在`logs/generated/`目录下生成以下文件：
- `scenario_1_cloudgpt_gpt-4o-mini-20240718_{timestamp}.log` - 原有执行日志
- `scenario_1_cloudgpt_gpt-4o-mini-20240718_{timestamp}.json` - 原有JSON跟踪
- `scenario_1_cloudgpt_gpt-4o-mini-20240718_{timestamp}_llm_calls.log` - 原有LLM调用日志
- `scenario_1_cloudgpt_gpt-4o-mini-20240718_{timestamp}_debug.log` - 新的debug日志

## Debug日志内容

### 1. 基本信息
- 场景ID、端点类型、模型名称
- 开始和结束时间
- 总步数和执行时长

### 2. 执行步骤
- 每个执行步骤的角色和内容预览
- 精确的时间戳

### 3. 代理交互
- 代理名称、动作类型、内容预览

### 4. 错误记录
- 异常类型和消息
- 发生上下文

## 示例输出

### Debug日志示例
```
=== DEBUG LOG STARTED ===
Scenario: 1
Endpoint: cloudgpt
Model: gpt-4o-mini-20240718
Timestamp: 20250802_072637UTC
Start Time: 2025-08-02 07:26:37 UTC
=== EXECUTION TRACE ===

[07:26:37.664] DEBUG: Starting Magnetic-One execution with cloudgpt model gpt-4o-mini-20240718
[07:26:37.665] DEBUG: Step 0 [human]: What is the capital of France?...
[07:26:38.123] DEBUG: Step 1 [Orchestrator]: I need to answer the question about the capital of France...
[07:26:38.124] DEBUG: Agent[Orchestrator] response: I need to answer the question about the capital of France...

=== DEBUG LOG COMPLETED ===
End Time: 2025-08-02 07:26:37 UTC
Total Steps: 5
Duration: 00:01:30
=== END DEBUG LOG ===
```

## API说明

### 主要函数
- `setup_debug_logging(scenario_id, endpoint_type, model, output_dir, run_timestamp)` - 初始化debug日志
- `log_debug_message(debug_log_path, message)` - 记录debug消息
- `finalize_debug_logging(debug_log_path, total_steps, duration)` - 完成debug日志

### DebugLogger类（向后兼容）
提供与之前复杂实现兼容的简单接口：
- `log_debug(message)` - 记录debug消息
- `log_execution_step(step_number, role, content)` - 记录执行步骤
- `log_agent_interaction(agent_name, action, content)` - 记录代理交互
- `log_error(error, context)` - 记录错误
- `finalize(total_steps, duration)` - 完成日志

## 与原有日志的关系

Debug日志遵循与原有日志相同的模式：
- **相同目录**：都保存在`logs/generated/`目录
- **相同命名**：使用相同的命名约定和时间戳
- **最小影响**：不修改原有代码和日志功能
- **简单实现**：提供基本的debug信息记录功能

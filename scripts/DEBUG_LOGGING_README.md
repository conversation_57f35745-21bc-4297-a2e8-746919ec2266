# Debug级别日志记录功能

## 概述

本功能为Magentic-One测试脚本添加了debug级别的日志记录，能够详细记录执行过程中的函数调用、代理交互、LLM调用等信息，保存在`log/generated/debug/`目录下。

## 特性

- **最小代码修改**：通过新增`debug_log.py`模块和新的运行脚本实现，不修改原有代码
- **详细执行跟踪**：记录函数调用、代理交互、LLM调用、错误等信息
- **双格式输出**：生成易读的`.log`文件和结构化的`.json`文件
- **性能统计**：提供执行性能指标和统计信息
- **时间戳精确**：所有日志条目都包含精确的UTC时间戳

## 文件结构

```
scripts/
├── run_m1_debug.py          # 新的运行脚本（包含debug功能）
├── test_debug_logging.py    # debug功能测试脚本
└── utils/
    ├── debug_log.py         # debug日志核心模块
    └── __init__.py          # 更新的导入配置

logs/generated/debug/        # debug日志输出目录
├── scenario_*_debug.log     # 易读的debug日志
└── scenario_*_debug.json    # 结构化的debug数据
```

## 使用方法

### 1. 运行带debug日志的测试

```bash
# 激活conda环境
conda activate m1

# 运行带debug日志的测试
cd /mnt/v-mingm/AutoDebugging/scripts
python run_m1_debug.py --scenario 1 --endpoint_type cloudgpt --model gpt-4o-mini-20240718
```

### 2. 测试debug功能

```bash
# 测试debug日志功能是否正常
python test_debug_logging.py
```

## 输出文件说明

### Debug日志文件 (.log)
- 位置：`logs/generated/debug/scenario_{id}_{endpoint}_{model}_{timestamp}_debug.log`
- 格式：易读的文本格式，包含时间戳和详细信息
- 内容：函数调用跟踪、代理交互、LLM调用、错误信息等

### Debug数据文件 (.json)
- 位置：`logs/generated/debug/scenario_{id}_{endpoint}_{model}_{timestamp}_debug.json`
- 格式：结构化JSON格式
- 内容：
  - `metadata`: 运行元数据和性能统计
  - `execution_trace`: 执行步骤跟踪
  - `function_calls`: 函数调用跟踪
  - `agent_interactions`: 代理交互记录
  - `llm_calls`: LLM调用统计
  - `errors`: 错误记录
  - `performance_metrics`: 性能指标

## Debug日志内容

### 1. 执行跟踪
- 每个执行步骤的详细信息
- 角色、内容长度、内容预览
- 精确的时间戳

### 2. 函数调用跟踪
- 使用`sys.settrace`跟踪相关模块的函数调用
- 包含函数名、文件名、行号、调用深度
- 仅跟踪autogen、magentic_one等相关模块

### 3. 代理交互
- 代理名称、动作类型、内容
- 步骤编号和时间戳

### 4. LLM调用统计
- 输入消息数量和字符数
- 响应字符数
- Token使用统计（如果可用）

### 5. 错误记录
- 异常类型和消息
- 发生上下文
- 时间戳

## 性能影响

- **函数跟踪**：使用`sys.settrace`会有一定性能开销，但仅跟踪相关模块
- **日志写入**：异步写入，对主执行流程影响最小
- **内存使用**：debug数据在内存中累积，完成后写入文件

## 配置选项

### DebugLogger类参数
- `scenario_id`: 场景ID
- `endpoint_type`: 端点类型（ollama/openai/cloudgpt）
- `model`: 模型名称
- `output_dir`: 输出目录
- `run_timestamp`: 运行时间戳（可选）

### 跟踪模块配置
在`debug_log.py`中可以修改`traced_modules`来调整跟踪的模块：
```python
self.traced_modules = {'autogen', 'magentic_one', '__main__'}
```

## 示例输出

### Debug日志示例
```
=== DEBUG LOG STARTED ===
Scenario: 1
Endpoint: cloudgpt
Model: gpt-4o-mini-20240718
Timestamp: 20250802_061654UTC
Start Time: 2025-08-02 06:16:54 UTC
Debug Level: DETAILED
=== EXECUTION TRACE ===

[06:16:54.190] DEBUG: Starting Magnetic-One execution with cloudgpt model gpt-4o-mini-20240718
[06:16:54.191] DEBUG: Step 1 [user]: 25 chars - What is the capital of France?...
[06:16:54.192] DEBUG: Agent[Orchestrator] process: Processing user query...
[06:16:54.193] DEBUG: LLM Call[Orchestrator]: 3 msgs → 150 chars
```

### 性能统计示例
```
=== DEBUG LOG COMPLETED ===
End Time: 2025-08-02 06:18:30 UTC
Total Steps: 15
Duration: 00:01:36
Function Calls: 245
Agent Interactions: 18
LLM Calls: 8
Errors: 0
```

## 故障排除

### 1. 导入错误
确保在scripts目录下运行，或者正确设置Python路径。

### 2. 权限问题
确保对`logs/generated/debug/`目录有写权限。

### 3. 函数跟踪不工作
检查`traced_modules`配置是否包含相关模块名。

### 4. 性能问题
如果性能影响过大，可以：
- 减少跟踪的模块
- 调整日志详细程度
- 禁用函数跟踪功能

## 与原有日志的关系

Debug日志是对现有日志系统的补充：
- **原有日志**：保持不变，记录主要执行流程
- **LLM调用日志**：保持不变，记录LLM调用详情
- **Debug日志**：新增，记录详细的内部执行信息

所有日志文件使用相同的命名约定和时间戳，便于关联分析。

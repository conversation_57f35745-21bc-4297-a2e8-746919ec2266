# Debug日志功能使用示例

## 快速开始

### 1. 基本使用

```bash
# 激活conda环境
conda activate m1

# 进入scripts目录
cd /mnt/v-mingm/AutoDebugging/scripts

# 运行带debug日志的测试（推荐参数）
python run_m1_debug.py --scenario 1 --endpoint_type cloudgpt --model gpt-4o-mini-20240718
```

### 2. 测试debug功能

```bash
# 测试debug日志基本功能
python test_debug_logging.py

# 测试简化版debug功能（不需要完整的Magentic-One环境）
python test_debug_simple.py
```

## 输出文件位置

所有debug日志文件保存在 `logs/generated/debug/` 目录下：

```
logs/generated/debug/
├── scenario_1_cloudgpt_gpt-4o-mini-20240718_20250802_062125UTC_debug.log
└── scenario_1_cloudgpt_gpt-4o-mini-20240718_20250802_062125UTC_debug.json
```

## 文件命名规则

```
scenario_{场景ID}_{端点类型}_{模型名}_{时间戳}_debug.{扩展名}
```

例如：
- `scenario_1_cloudgpt_gpt-4o-mini-20240718_20250802_062125UTC_debug.log`
- `scenario_1_cloudgpt_gpt-4o-mini-20240718_20250802_062125UTC_debug.json`

## Debug日志内容示例

### .log文件（易读格式）
```
=== DEBUG LOG STARTED ===
Scenario: 1
Endpoint: cloudgpt
Model: gpt-4o-mini-20240718
Timestamp: 20250802_062125UTC
Start Time: 2025-08-02 06:21:25 UTC
Debug Level: DETAILED
=== EXECUTION TRACE ===

[06:21:25.829] DEBUG: Starting Magnetic-One execution with cloudgpt model gpt-4o-mini-20240718
[06:21:25.829] DEBUG: Step 1 [human]: 30 chars - What is the capital of France?...
[06:21:25.929] DEBUG: Agent[orchestrator] process: Processing user query...
[06:21:26.030] DEBUG: LLM Call[orchestrator]: 3 msgs → 150 chars
...

=== DEBUG LOG COMPLETED ===
End Time: 2025-08-02 06:21:26 UTC
Total Steps: 15
Duration: 00:01:36
Function Calls: 245
Agent Interactions: 18
LLM Calls: 8
Errors: 0
```

### .json文件（结构化格式）
```json
{
  "metadata": {
    "scenario_id": "1",
    "endpoint_type": "cloudgpt",
    "model": "gpt-4o-mini-20240718",
    "run_timestamp": "20250802_062125UTC",
    "start_time": "2025-08-02T06:21:25.829348+00:00",
    "debug_level": "DETAILED",
    "end_time": "2025-08-02T06:21:26.432115+00:00",
    "total_steps": 6,
    "duration": "00:00:05"
  },
  "execution_trace": [...],
  "function_calls": [...],
  "agent_interactions": [...],
  "llm_calls": [...],
  "errors": [...],
  "performance_metrics": {
    "total_function_calls": 245,
    "total_agent_interactions": 18,
    "total_llm_calls": 8,
    "total_errors": 0,
    "total_execution_steps": 15
  }
}
```

## 与原有日志的对比

| 日志类型 | 位置 | 内容 | 用途 |
|---------|------|------|------|
| **原有执行日志** | `logs/generated/scenario_*.log` | 主要执行步骤 | 查看整体执行流程 |
| **LLM调用日志** | `logs/generated/scenario_*_llm_calls.log` | LLM调用详情 | 分析LLM使用情况 |
| **Debug日志** | `logs/generated/debug/scenario_*_debug.log` | 详细内部执行信息 | 深度调试和分析 |

## 性能统计

Debug日志提供详细的性能统计：

- **函数调用跟踪**：记录内部函数调用次数和层级
- **代理交互统计**：各代理的交互次数和类型
- **LLM调用分析**：Token使用量和调用频率
- **错误记录**：异常类型和发生上下文
- **执行时间分析**：各步骤的时间戳和耗时

## 自定义配置

### 修改跟踪的模块

在 `scripts/utils/debug_log.py` 中修改：

```python
self.traced_modules = {'autogen', 'magentic_one', '__main__', 'your_module'}
```

### 调整日志详细程度

可以通过修改 `DebugLogger` 类的方法来调整日志的详细程度：

```python
# 在 log_execution_step 方法中调整内容预览长度
content_preview = content[:200] + "..." if len(content) > 200 else content
```

## 故障排除

### 1. 模块导入错误
确保在正确的conda环境中运行：
```bash
conda activate m1
```

### 2. 权限问题
确保对日志目录有写权限：
```bash
chmod 755 logs/generated/debug/
```

### 3. 性能影响
如果debug日志影响性能，可以：
- 减少跟踪的模块数量
- 调整日志写入频率
- 禁用函数跟踪功能

### 4. 磁盘空间
Debug日志可能产生大量数据，定期清理旧文件：
```bash
# 删除7天前的debug日志
find logs/generated/debug/ -name "*.log" -mtime +7 -delete
find logs/generated/debug/ -name "*.json" -mtime +7 -delete
```

## 最佳实践

1. **定期备份重要的debug日志**
2. **使用时间戳来区分不同的运行**
3. **结合JSON文件进行数据分析**
4. **监控磁盘空间使用情况**
5. **根据需要调整跟踪的详细程度**

## 技术支持

如果遇到问题，请检查：
1. `scripts/DEBUG_LOGGING_README.md` - 详细文档
2. `scripts/test_debug_simple.py` - 简化测试
3. `scripts/utils/debug_log.py` - 核心实现

所有debug功能都设计为最小侵入性，不会影响原有的代码和日志系统。

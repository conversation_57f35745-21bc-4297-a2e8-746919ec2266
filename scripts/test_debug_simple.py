#!/usr/bin/env python3
"""
Simple Debug Logging Test
========================

This script tests the debug logging functionality without requiring
the full Magentic-One system to be available.
"""

import sys
import time
import json
from pathlib import Path
from datetime import datetime, timezone

# Add the scripts directory to the path so we can import utils
sys.path.insert(0, str(Path(__file__).parent))

from utils import setup_debug_logging

def simulate_magentic_one_execution():
    """Simulate a simple Magentic-One execution for testing."""
    print("Simulating Magentic-One execution...")
    
    # Setup parameters
    scenario_id = "test_simple"
    endpoint_type = "cloudgpt"
    model = "gpt-4o-mini-20240718"
    output_dir = Path(__file__).parent.parent / "logs/generated"
    
    # Create debug logger
    debug_logger = setup_debug_logging(scenario_id, endpoint_type, model, output_dir)
    
    # Start tracing
    debug_logger.start_tracing()
    debug_logger.log_debug("Starting simulated execution")
    
    # Simulate some execution steps
    steps = [
        {"role": "human", "content": "What is the capital of France?"},
        {"role": "orchestrator", "content": "I need to search for information about the capital of France."},
        {"role": "websurfer", "content": "Searching for 'capital of France'..."},
        {"role": "websurfer", "content": "Found result: Paris is the capital of France."},
        {"role": "orchestrator", "content": "Based on the search results, I can provide the answer."},
        {"role": "assistant", "content": "The capital of France is Paris."}
    ]
    
    for i, step in enumerate(steps, 1):
        debug_logger.log_execution_step(i, step["role"], step["content"])
        debug_logger.log_agent_interaction(step["role"], "process", step["content"], i)
        
        # Simulate some processing time
        time.sleep(0.1)
        
        # Simulate LLM call for some steps
        if step["role"] in ["orchestrator", "websurfer", "assistant"]:
            messages = [{"role": "user", "content": "Previous context"}, {"role": "user", "content": step["content"]}]
            response = {
                "content": step["content"],
                "usage": {"prompt_tokens": len(step["content"]) // 4, "completion_tokens": len(step["content"]) // 6}
            }
            debug_logger.log_llm_call(messages, response, step["role"])
    
    # Simulate an error
    try:
        raise ValueError("This is a simulated error for testing")
    except Exception as e:
        debug_logger.log_error(e, "simulate_execution")
    
    # Stop tracing
    debug_logger.stop_tracing()
    
    # Finalize
    debug_logger.finalize(len(steps), "00:00:05")
    
    print(f"\n✓ Simulation completed successfully!")
    print(f"✓ Debug log: {debug_logger.debug_log_path}")
    print(f"✓ Debug JSON: {debug_logger.debug_log_path.with_suffix('.json')}")
    
    # Show some statistics
    stats = debug_logger.debug_data["performance_metrics"]
    print(f"✓ Statistics:")
    print(f"  - Function calls: {stats['total_function_calls']}")
    print(f"  - Agent interactions: {stats['total_agent_interactions']}")
    print(f"  - LLM calls: {stats['total_llm_calls']}")
    print(f"  - Errors: {stats['total_errors']}")
    print(f"  - Execution steps: {stats['total_execution_steps']}")
    
    return debug_logger.debug_log_path

def test_function_tracing():
    """Test function call tracing."""
    print("\nTesting function call tracing...")
    
    def test_func_a():
        time.sleep(0.05)
        return test_func_b()
    
    def test_func_b():
        time.sleep(0.05)
        return test_func_c()
    
    def test_func_c():
        time.sleep(0.05)
        return "result"
    
    result = test_func_a()
    print(f"Function tracing test result: {result}")

if __name__ == "__main__":
    print("Simple Debug Logging Test")
    print("=" * 40)
    
    # Test basic debug logging
    debug_log_path = simulate_magentic_one_execution()
    
    # Show log file preview
    print(f"\nDebug Log Preview (first 15 lines):")
    print("-" * 40)
    with debug_log_path.open("r", encoding="utf-8") as f:
        for i, line in enumerate(f):
            if i >= 15:
                print("...")
                break
            print(f"{i+1:2d}: {line.rstrip()}")
    
    # Show JSON data preview
    json_path = debug_log_path.with_suffix('.json')
    print(f"\nDebug JSON Preview (metadata):")
    print("-" * 40)
    with json_path.open("r", encoding="utf-8") as f:
        data = json.load(f)
        print(json.dumps(data["metadata"], indent=2))
    
    print(f"\n✓ All tests completed successfully!")
    print(f"✓ Files created in: {debug_log_path.parent}")
